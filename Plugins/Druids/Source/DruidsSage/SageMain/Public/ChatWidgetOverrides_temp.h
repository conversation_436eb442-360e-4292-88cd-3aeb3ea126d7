#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "DruidsSageChatShell.h"
#include "ChatWidgetOverrides.generated.h"

/**
 * ChatWidgetOverrides class that can be overridden in Blueprint to specify
 * which UDruidsSageChatShell class to instantiate instead of the base class.
 * This allows for Blueprint customization of the chat shell widget.
 */
UCLASS(Blueprintable, BlueprintType)
class SAGEMAIN_API UChatWidgetOverrides : public UObject
{
	GENERATED_BODY()

public:
	UChatWidgetOverrides();

	/**
	 * Gets the widget class to instantiate for the chat shell.
	 * Can be overridden in Blueprint to return a custom UDruidsSageChatShell subclass.
	 * @return The UDruidsSageChatShell class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageChatShell> GetChatShellWidgetClass() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetChatShellWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageChatShell> GetChatShellWidgetClass_Implementation() const;

protected:
	/**
	 * The default chat shell widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageChatShell.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageChatShell> DefaultChatShellClass;
};
